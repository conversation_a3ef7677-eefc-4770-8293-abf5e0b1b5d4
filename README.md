# Trio - Modern Streetwear Website

A modern, responsive website for Trio, a clothing brand founded by three friends from Syria. The website showcases trendy, affordable streetwear with a clean, contemporary design.

## 🌟 Features

### Design & User Experience
- **Modern Streetwear Aesthetic**: Clean design with streetwear-inspired color palette
- **Fully Responsive**: Mobile-first design that works on all devices
- **Smooth Animations**: Subtle animations and transitions for enhanced user experience
- **Interactive Elements**: Hover effects, smooth scrolling, and dynamic interactions

### Sections
- **Hero Banner**: Eye-catching introduction with call-to-action buttons
- **Featured Collections**: Showcase of main product categories
- **Product Gallery**: Interactive product cards with hover effects and color options
- **About Us**: Brand story highlighting the three founders from Syria
- **Contact Form**: Functional contact form with validation
- **Sticky Footer**: Social media links and company information

### Technical Features
- **Semantic HTML5**: Proper markup for accessibility and SEO
- **Modern CSS**: CSS Grid, Flexbox, custom properties (CSS variables)
- **Vanilla JavaScript**: No dependencies, lightweight and fast
- **Form Validation**: Client-side form validation with user feedback
- **Mobile Navigation**: Responsive hamburger menu for mobile devices
- **Scroll Effects**: Intersection Observer API for scroll-triggered animations
- **Performance Optimized**: Debounced scroll events and optimized animations

## 🚀 Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- No server required - runs directly in the browser

### Installation
1. Download or clone the project files
2. Open `index.html` in your web browser
3. That's it! The website is ready to use

### File Structure
```
trio-website/
├── index.html              # Main HTML file
├── assets/
│   ├── css/
│   │   └── styles.css      # All CSS styles
│   ├── js/
│   │   └── main.js         # JavaScript functionality
│   └── images/             # Image assets (placeholder folder)
└── README.md               # This file
```

## 🎨 Design System

### Color Palette
- **Primary**: #1a1a1a (Dark charcoal)
- **Secondary**: #f5f5f5 (Light gray)
- **Accent**: #ff6b35 (Orange)
- **Accent Secondary**: #4ecdc4 (Teal)
- **Background**: #ffffff (White)
- **Background Secondary**: #f8f9fa (Off-white)

### Typography
- **Primary Font**: Inter (Sans-serif)
- **Display Font**: Playfair Display (Serif)
- **Font Sizes**: Responsive scale from 0.75rem to 3rem

### Spacing System
- **Base Unit**: 1rem (16px)
- **Scale**: 0.25rem, 0.5rem, 1rem, 1.5rem, 2rem, 3rem, 4rem

## 📱 Responsive Breakpoints

- **Mobile**: < 576px
- **Tablet**: 576px - 768px
- **Desktop**: 768px - 992px
- **Large Desktop**: 992px - 1200px
- **Extra Large**: > 1200px

## ✨ Interactive Features

### Navigation
- Fixed navigation bar with scroll effects
- Mobile hamburger menu
- Smooth scrolling to sections
- Active link highlighting

### Product Cards
- Hover effects with overlay buttons
- Color option selection
- Quick view functionality (placeholder)
- Responsive grid layout

### Contact Form
- Real-time validation
- Error message display
- Success notifications
- Accessible form labels

### Additional Features
- Scroll-to-top button
- Loading states for form submission
- Notification system
- Intersection Observer animations

## 🔧 Customization

### Adding New Products
1. Copy an existing `.product-card` div in `index.html`
2. Update the product name, price, and color options
3. Add appropriate Font Awesome icon or replace with actual images

### Changing Colors
1. Update CSS custom properties in `:root` section of `styles.css`
2. Colors will automatically update throughout the site

### Adding New Sections
1. Add HTML structure following existing patterns
2. Add corresponding CSS styles
3. Update navigation links if needed

## 🌐 Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers (iOS Safari, Chrome Mobile)

## 📞 Contact Information

For questions about this website or the Trio brand:
- **Email**: <EMAIL>
- **WhatsApp**: +963 XXX XXX XXX
- **Instagram**: @trio_clothing

## 📄 License

This project is created for Trio Clothing Brand. All rights reserved.

---

**Made with ❤️ for Trio - Modern Streetwear from Syria**
