# Trio Parallax Landing Page Documentation

## Overview
A stunning parallax scrolling landing page designed for the Trio brand that creates visual depth and enhances storytelling through smooth animations and multi-layered content movement.

## Features Implemented

### ✅ Core Requirements
- **3+ Parallax Layers**: Background elements, floating shapes, and content move at different speeds
- **Responsive Design**: Fully optimized for mobile and desktop
- **Smooth Performance**: Uses `requestAnimationFrame` for 60fps animations
- **No Layout Shifts**: Proper CSS transforms prevent reflow/repaint issues
- **No Heavy Dependencies**: Pure HTML/CSS/JavaScript implementation

### ✅ Stretch Goals Achieved
- **Fade-in Animations**: Story sections animate in using Intersection Observer
- **Scroll Progress Indicator**: Visual progress bar at the top
- **Sticky Navigation**: Auto-highlighting current section
- **Floating Background Elements**: Subtle animated shapes for atmosphere

## Technical Implementation

### Parallax Effect Mechanism
```javascript
function updateParallax() {
    const scrolled = window.pageYOffset;
    const parallaxElements = document.querySelectorAll('[data-speed]');
    
    parallaxElements.forEach(element => {
        const speed = element.dataset.speed;
        const yPos = -(scrolled * speed);
        element.style.transform = `translateY(${yPos}px)`;
    });
}
```

**How it works:**
1. **Data Attributes**: Each parallax element has a `data-speed` attribute (0.1-0.8)
2. **Transform Calculation**: `translateY = -(scrollPosition × speed)`
3. **Performance Optimization**: Uses `requestAnimationFrame` to prevent jank
4. **Layer Speeds**:
   - Background: 0.5 (slower)
   - Floating elements: 0.3-0.7 (varied speeds)
   - Content: 1.0 (normal scroll speed)

### Animation System
- **Intersection Observer**: Triggers animations when elements enter viewport
- **CSS Transitions**: Smooth 0.8s ease transitions for content
- **Transform-based**: Uses `translateY` and `opacity` for performance

### Responsive Strategy
- **Mobile Optimization**: Disables `background-attachment: fixed` on mobile
- **Grid Layouts**: Switches to single column on small screens
- **Touch-friendly**: Larger touch targets and simplified navigation

## File Structure
```
parallax-landing.html (standalone file)
├── HTML Structure
├── Embedded CSS (all styles)
└── Embedded JavaScript (all functionality)
```

## Customization Guide

### Replacing Images
Replace the placeholder SVG images with your actual images:

```html
<!-- Current placeholder -->
<img src="data:image/svg+xml,..." alt="Origin Story">

<!-- Replace with -->
<img src="assets/images/origin-story.jpg" alt="Origin Story">
```

**Recommended image specifications:**
- **Format**: JPG/WebP for photos, PNG for graphics
- **Size**: 800x600px minimum for story images
- **Optimization**: Compress images for web (under 500KB each)

### Updating Content
1. **Hero Section**: Edit title, tagline, and description
2. **Story Sections**: Update headings, paragraphs, and images
3. **CTA Section**: Modify call-to-action text and button link

### Customizing Colors
Update CSS variables in the `:root` selector:
```css
:root {
    --primary-color: #2d1b3d;      /* Dark purple */
    --accent-color: #a855f7;       /* Purple */
    --accent-secondary: #c084fc;   /* Light purple */
    /* Add your brand colors here */
}
```

### Adjusting Parallax Speeds
Modify `data-speed` attributes:
- **0.1-0.3**: Very slow (distant background)
- **0.4-0.6**: Medium (mid-ground elements)
- **0.7-0.9**: Fast (foreground elements)

### Adding New Sections
1. **HTML Structure**:
```html
<section id="new-section" class="story-section">
    <div class="story-content">
        <!-- Your content here -->
    </div>
    <div class="floating-element shape-1" data-speed="0.4"></div>
</section>
```

2. **Update Navigation**:
```html
<li><a href="#new-section" class="nav-link">New Section</a></li>
```

## Performance Optimizations

### Implemented Optimizations
- **RequestAnimationFrame**: Smooth 60fps animations
- **Transform-only Animations**: Avoids layout/paint operations
- **Intersection Observer**: Efficient scroll-based animations
- **CSS `will-change`**: Optimizes GPU acceleration
- **Debounced Events**: Prevents excessive function calls

### Browser Compatibility
- **Modern Browsers**: Full support (Chrome 60+, Firefox 55+, Safari 12+)
- **Mobile**: Optimized for iOS Safari and Chrome Mobile
- **Fallbacks**: Graceful degradation for older browsers

## Deployment Instructions

### Option 1: Standalone File
- Upload `parallax-landing.html` to your web server
- Access directly via URL

### Option 2: Integration with Existing Site
1. Extract CSS to external file
2. Extract JavaScript to external file
3. Include in your existing HTML structure

### Option 3: Replace Current Landing Page
- Backup your current `index.html`
- Rename `parallax-landing.html` to `index.html`
- Update asset paths if needed

## Troubleshooting

### Common Issues
1. **Jerky Scrolling**: Check if `background-attachment: fixed` is causing issues on mobile
2. **Images Not Loading**: Verify image paths and file permissions
3. **Animations Not Working**: Ensure JavaScript is enabled and no console errors

### Performance Tips
- Optimize images before deployment
- Consider lazy loading for below-fold images
- Test on various devices and connection speeds

## Browser Testing Checklist
- [ ] Chrome Desktop/Mobile
- [ ] Firefox Desktop/Mobile
- [ ] Safari Desktop/Mobile
- [ ] Edge Desktop
- [ ] Test on slow connections
- [ ] Test with JavaScript disabled

## Future Enhancements
- Add video backgrounds for hero section
- Implement mouse parallax effects
- Add scroll-triggered sound effects
- Create loading animations
- Add dark/light mode toggle

---

**Created for Trio Brand**  
*Modern parallax storytelling experience*
