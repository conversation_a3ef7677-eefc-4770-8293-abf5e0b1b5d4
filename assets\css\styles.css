/* ===== CSS RESET & BASE STYLES ===== */
*,
*::before,
*::after {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* ===== CSS VARIABLES ===== */
:root {
    /* Colors - Purple Gradient Theme from Business Card */
    --primary-color: #2d1b3d;
    --secondary-color: #f5f3ff;
    --accent-color: #a855f7;
    --accent-secondary: #c084fc;
    --purple-light: #e9d5ff;
    --purple-medium: #8b5cf6;
    --purple-dark: #1e1b4b;
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --text-light: #ffffff;
    --background-primary: #ffffff;
    --background-secondary: #faf7ff;
    --background-dark: #1e1b4b;
    --border-color: #e5e7eb;
    --shadow-light: 0 2px 10px rgba(139, 92, 246, 0.1);
    --shadow-medium: 0 4px 20px rgba(139, 92, 246, 0.15);
    --shadow-heavy: 0 8px 30px rgba(139, 92, 246, 0.2);

    /* Gradient Colors */
    --gradient-primary: linear-gradient(135deg, #a855f7 0%, #c084fc 50%, #e879f9 100%);
    --gradient-secondary: linear-gradient(135deg, #1e1b4b 0%, #2d1b3d 50%, #4c1d95 100%);
    --gradient-accent: linear-gradient(45deg, #8b5cf6, #a855f7);
    
    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-display: 'Playfair Display', Georgia, serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    --font-size-5xl: 3rem;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;
    
    /* Border Radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Breakpoints */
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1200px;
    --breakpoint-2xl: 1400px;
}

/* ===== BASE STYLES ===== */
html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-normal);
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-display);
    font-weight: var(--font-weight-semibold);
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-5xl); }
h2 { font-size: var(--font-size-4xl); }
h3 { font-size: var(--font-size-3xl); }
h4 { font-size: var(--font-size-2xl); }
h5 { font-size: var(--font-size-xl); }
h6 { font-size: var(--font-size-lg); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-color);
}

/* ===== UTILITY CLASSES ===== */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-flex { display: flex; }
.d-grid { display: grid; }
.d-none { display: none; }
.d-block { display: block; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.align-center { align-items: center; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-sm); }
.mb-2 { margin-bottom: var(--spacing-md); }
.mb-3 { margin-bottom: var(--spacing-lg); }
.mb-4 { margin-bottom: var(--spacing-xl); }

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-xl);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    min-width: 120px;
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--text-light);
    border-color: transparent;
    box-shadow: 0 4px 15px rgba(139, 92, 246, 0.3);
}

.btn-primary:hover {
    background: var(--gradient-secondary);
    border-color: transparent;
    color: var(--text-light);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(139, 92, 246, 0.4);
}

.btn-secondary {
    background-color: transparent;
    color: var(--text-light);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    border-color: var(--text-light);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

.btn-overlay {
    background-color: var(--accent-color);
    color: var(--text-light);
    border: none;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-sm);
    min-width: auto;
}

.btn-overlay:hover {
    background-color: var(--primary-color);
    transform: translateY(-1px);
}

/* ===== SECTION HEADERS ===== */
.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-title {
    font-size: var(--font-size-4xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: var(--radius-sm);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--text-secondary);
    font-weight: var(--font-weight-normal);
    margin-bottom: 0;
}

/* ===== RESPONSIVE GRID SYSTEM ===== */
.grid {
    display: grid;
    gap: var(--spacing-lg);
}

.grid-2 { grid-template-columns: repeat(2, 1fr); }
.grid-3 { grid-template-columns: repeat(3, 1fr); }
.grid-4 { grid-template-columns: repeat(4, 1fr); }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --font-size-5xl: 2.5rem;
        --font-size-4xl: 2rem;
        --font-size-3xl: 1.5rem;
    }
    
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .grid-2,
    .grid-3,
    .grid-4 {
        grid-template-columns: 1fr;
    }
    
    .btn {
        padding: var(--spacing-sm) var(--spacing-lg);
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 576px) {
    .section-header {
        margin-bottom: var(--spacing-2xl);
    }
    
    .section-title {
        font-size: var(--font-size-3xl);
    }
    
    .section-subtitle {
        font-size: var(--font-size-base);
    }
}

/* ===== NAVIGATION ===== */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: var(--background-primary);
    box-shadow: var(--shadow-light);
    z-index: 1000;
    transition: all var(--transition-normal);
}

.navbar.scrolled {
    background-color: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo a {
    text-decoration: none;
    display: flex;
    align-items: center;
}

.logo-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.logo-main {
    display: flex;
    align-items: center;
    gap: 8px;
}

.logo-icon {
    font-size: 1.2rem;
    color: var(--accent-color);
    transform: rotate(-10deg);
}

.logo-name {
    font-family: 'Dancing Script', cursive;
    font-size: 2rem;
    font-weight: 600;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-style: italic;
}

.logo-tagline {
    font-size: 0.6rem;
    color: var(--accent-color);
    font-family: var(--font-primary);
    font-weight: var(--font-weight-bold);
    letter-spacing: 2px;
    text-transform: uppercase;
    margin-left: 20px;
    margin-top: -5px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: var(--spacing-xl);
}

.nav-link {
    color: var(--text-primary);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: var(--font-size-sm);
    position: relative;
    transition: color var(--transition-fast);
}

.nav-link:hover {
    color: var(--accent-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--accent-color);
    transition: width var(--transition-normal);
}

.nav-link:hover::after {
    width: 100%;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background-color: var(--primary-color);
    transition: all var(--transition-normal);
    border-radius: var(--radius-sm);
}

/* Mobile Navigation */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background-color: var(--background-primary);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: var(--spacing-3xl);
        transition: left var(--transition-normal);
        box-shadow: var(--shadow-medium);
    }

    .logo-name {
        font-size: 1.5rem;
    }

    .logo-tagline {
        font-size: 0.5rem;
        letter-spacing: 1px;
        margin-left: 15px;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-item {
        margin: var(--spacing-lg) 0;
    }

    .nav-link {
        font-size: var(--font-size-lg);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .bar:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active .bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .bar:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* ===== HERO SECTION ===== */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--gradient-primary);
    padding-top: 80px;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    pointer-events: none;
}

.hero::after {
    content: '';
    position: absolute;
    top: 20%;
    right: -10%;
    width: 300px;
    height: 300px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
    pointer-events: none;
}

.hero-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    position: relative;
    z-index: 1;
}

.hero-content {
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    margin-bottom: var(--spacing-xl);
}

.hero-subtitle {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: var(--spacing-sm);
    font-family: var(--font-primary);
}

.hero-main-title {
    display: block;
    font-size: var(--font-size-5xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-light);
    margin-bottom: var(--spacing-sm);
    letter-spacing: 3px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-tagline {
    display: block;
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--text-light);
    font-family: var(--font-primary);
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 3px;
    margin-bottom: var(--spacing-sm);
}

.hero-subtitle-2 {
    display: block;
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-normal);
    color: rgba(255, 255, 255, 0.8);
    font-family: var(--font-primary);
    font-style: italic;
}

.hero-description {
    font-size: var(--font-size-lg);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: var(--spacing-2xl);
    max-width: 500px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
    animation: fadeInRight 1s ease-out 0.3s both;
}

.hero-image-placeholder {
    width: 300px;
    height: 300px;
    background: var(--gradient-secondary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 80px;
    color: var(--text-light);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.4);
    animation: float 6s ease-in-out infinite;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@media (max-width: 768px) {
    .hero-container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: var(--spacing-2xl);
    }

    .hero-main-title {
        font-size: var(--font-size-4xl);
    }

    .hero-image-placeholder {
        width: 200px;
        height: 200px;
        font-size: 60px;
    }

    .hero-buttons {
        justify-content: center;
    }
}

/* ===== FEATURED COLLECTIONS ===== */
.featured-collections {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--background-secondary) 0%, rgba(168, 85, 247, 0.05) 100%);
    position: relative;
}

.featured-collections::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60"><defs><pattern id="hexagon" width="60" height="60" patternUnits="userSpaceOnUse"><polygon points="30,5 50,17.5 50,42.5 30,55 10,42.5 10,17.5" fill="none" stroke="%23a855f7" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100%" height="100%" fill="url(%23hexagon)"/></svg>');
    pointer-events: none;
    opacity: 0.3;
}

.collections-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-2xl);
}

.collection-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    transition: all var(--transition-normal);
    cursor: pointer;
}

.collection-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.collection-image {
    height: 250px;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.collection-image .image-placeholder {
    font-size: 60px;
    color: var(--text-light);
    transition: transform var(--transition-normal);
}

.collection-card:hover .image-placeholder {
    transform: scale(1.1);
}

.collection-info {
    padding: var(--spacing-xl);
}

.collection-info h3 {
    font-size: var(--font-size-xl);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
}

.collection-info p {
    color: var(--text-secondary);
    margin-bottom: var(--spacing-lg);
}

.collection-link {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--accent-color);
    font-weight: var(--font-weight-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.collection-link:hover {
    color: var(--primary-color);
    gap: var(--spacing-md);
}

/* ===== PRODUCTS SECTION ===== */
.products {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-primary);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-2xl);
}

.product-card {
    background-color: var(--background-primary);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.1);
    transition: all var(--transition-normal);
    position: relative;
    border: 1px solid rgba(168, 85, 247, 0.1);
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.2);
    border-color: rgba(168, 85, 247, 0.3);
}

.product-image {
    height: 300px;
    background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.product-image .image-placeholder {
    font-size: 50px;
    color: var(--text-secondary);
    transition: transform var(--transition-normal);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-card:hover .image-placeholder {
    transform: scale(1.1);
}

.product-info {
    padding: var(--spacing-lg);
}

.product-name {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-weight: var(--font-weight-medium);
}

.product-price {
    font-size: var(--font-size-xl);
    color: var(--accent-color);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
}

.product-colors {
    display: flex;
    gap: var(--spacing-sm);
}

.color-option {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid var(--border-color);
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.color-option:hover {
    transform: scale(1.2);
    border-color: var(--accent-color);
}

.color-option.active {
    border-color: var(--accent-color);
    border-width: 3px;
    transform: scale(1.1);
}

.color-option[style*="background-color: #fff"] {
    border-color: var(--text-secondary);
}

/* ===== ABOUT SECTION ===== */
.about {
    padding: var(--spacing-3xl) 0;
    background-color: var(--background-secondary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: center;
}

.about-text .section-header {
    text-align: left;
    margin-bottom: var(--spacing-2xl);
}

.about-text .section-title::after {
    left: 0;
    transform: none;
}

.about-story p {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    margin-bottom: var(--spacing-lg);
}

.about-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.about-image .image-placeholder {
    width: 400px;
    height: 400px;
    background: var(--gradient-secondary);
    border-radius: var(--radius-xl);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 100px;
    color: var(--text-light);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
}

@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .about-text .section-header {
        text-align: center;
    }

    .about-text .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .about-image .image-placeholder {
        width: 250px;
        height: 250px;
        font-size: 60px;
    }
}

/* ===== CONTACT SECTION ===== */
.contact {
    padding: var(--spacing-3xl) 0;
    background: linear-gradient(135deg, var(--background-primary) 0%, rgba(168, 85, 247, 0.02) 100%);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-3xl);
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-2xl);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    background-color: var(--background-secondary);
    border-radius: var(--radius-lg);
    transition: transform var(--transition-normal);
}

.contact-item:hover {
    transform: translateX(10px);
}

.contact-item i {
    font-size: var(--font-size-2xl);
    color: var(--accent-color);
    width: 40px;
    text-align: center;
}

.contact-item h4 {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

.contact-item p {
    color: var(--text-secondary);
    margin-bottom: 0;
}

.contact-link {
    color: var(--accent-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
    transition: color var(--transition-fast);
}

.contact-link:hover {
    color: var(--accent-secondary);
}

.contact-form {
    background-color: var(--background-secondary);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-light);
}

.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-sm);
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    background-color: var(--background-primary);
    color: var(--text-primary);
    transition: border-color var(--transition-fast);
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
    }

    .contact-form {
        padding: var(--spacing-lg);
    }
}

/* ===== FOOTER ===== */
.footer {
    background-color: var(--background-dark);
    color: var(--text-light);
    padding: var(--spacing-3xl) 0 var(--spacing-lg);
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: var(--spacing-3xl);
    margin-bottom: var(--spacing-2xl);
}

.footer-logo-text h3 {
    font-family: 'Dancing Script', cursive;
    font-size: var(--font-size-3xl);
    color: var(--text-light);
    margin-bottom: var(--spacing-xs);
    font-weight: 600;
    font-style: italic;
}

.footer-tagline {
    color: var(--accent-color) !important;
    font-size: var(--font-size-sm) !important;
    font-weight: var(--font-weight-bold);
    letter-spacing: 2px;
    margin-bottom: var(--spacing-md) !important;
    text-transform: uppercase;
}

.footer-brand p {
    color: rgba(255, 255, 255, 0.7);
    font-size: var(--font-size-base);
}

.footer-links {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2xl);
}

.footer-section h4 {
    color: var(--text-light);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-lg);
    font-weight: var(--font-weight-semibold);
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: var(--spacing-sm);
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.footer-section ul li a:hover {
    color: var(--accent-color);
}

.social-links {
    display: flex;
    gap: var(--spacing-md);
}

.social-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--text-light);
    border-radius: 50%;
    text-decoration: none;
    font-size: var(--font-size-lg);
    transition: all var(--transition-normal);
}

.social-link:hover {
    background-color: var(--accent-color);
    color: var(--text-light);
    transform: translateY(-3px);
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: var(--spacing-lg);
    text-align: center;
}

.footer-bottom p {
    color: rgba(255, 255, 255, 0.5);
    font-size: var(--font-size-sm);
    margin-bottom: 0;
}

@media (max-width: 768px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-2xl);
        text-align: center;
    }

    .footer-links {
        grid-template-columns: 1fr;
        gap: var(--spacing-xl);
    }

    .social-links {
        justify-content: center;
    }
}

/* ===== SCROLL TO TOP BUTTON ===== */
.scroll-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background-color: var(--accent-color);
    color: var(--text-light);
    border: none;
    border-radius: 50%;
    font-size: var(--font-size-lg);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
    z-index: 1000;
    box-shadow: var(--shadow-medium);
}

.scroll-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.scroll-to-top:hover {
    background-color: var(--primary-color);
    transform: translateY(-3px);
}

@media (max-width: 768px) {
    .scroll-to-top {
        bottom: 20px;
        right: 20px;
        width: 45px;
        height: 45px;
    }
}
