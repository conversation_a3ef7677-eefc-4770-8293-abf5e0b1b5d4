<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trio - Parallax Brand Story</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* ===== CSS RESET & BASE STYLES ===== */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2d1b3d;
            --accent-color: #a855f7;
            --accent-secondary: #c084fc;
            --text-light: #ffffff;
            --text-dark: #1f2937;
            --text-secondary: #6b7280;
            --gradient-primary: linear-gradient(135deg, #a855f7 0%, #c084fc 50%, #e879f9 100%);
            --gradient-dark: linear-gradient(135deg, #1e1b4b 0%, #2d1b3d 50%, #4c1d95 100%);
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: var(--text-dark);
            overflow-x: hidden;
        }

        /* ===== SCROLL PROGRESS INDICATOR ===== */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 4px;
            background: var(--gradient-primary);
            z-index: 1000;
            transition: width 0.1s ease;
        }

        /* ===== STICKY NAVIGATION ===== */
        .navbar {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            z-index: 999;
            padding: 1rem 2rem;
            transition: all 0.3s ease;
            transform: translateY(-100%);
        }

        .navbar.visible {
            transform: translateY(0);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1200px;
            margin: 0 auto;
        }

        .nav-logo {
            font-size: 1.5rem;
            font-weight: bold;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }

        .nav-links a {
            text-decoration: none;
            color: var(--text-dark);
            font-weight: 500;
            transition: color 0.3s ease;
            position: relative;
        }

        .nav-links a:hover,
        .nav-links a.active {
            color: var(--accent-color);
        }

        .nav-links a.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 0;
            width: 100%;
            height: 2px;
            background: var(--accent-color);
        }

        /* ===== PARALLAX SECTIONS ===== */
        .parallax-section {
            position: relative;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        .parallax-bg {
            position: absolute;
            top: 0;
            left: 0;
            width: 120%;
            height: 120%;
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            will-change: transform;
        }

        .parallax-content {
            position: relative;
            z-index: 10;
            text-align: center;
            max-width: 800px;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .parallax-content.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        /* ===== HERO SECTION ===== */
        .hero {
            background: var(--gradient-dark);
            color: var(--text-light);
        }

        .hero .parallax-bg {
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
        }

        .hero .parallax-content {
            background: rgba(45, 27, 61, 0.9);
            color: var(--text-light);
        }

        .hero h1 {
            font-size: 4rem;
            font-weight: bold;
            margin-bottom: 1rem;
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero .tagline {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--accent-secondary);
            margin-bottom: 1rem;
            letter-spacing: 2px;
        }

        .hero p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 2rem;
        }

        /* ===== STORY SECTIONS ===== */
        .story-section {
            min-height: 100vh;
            padding: 4rem 2rem;
            position: relative;
        }

        .story-content {
            max-width: 1200px;
            margin: 0 auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            opacity: 0;
            transform: translateY(50px);
            transition: all 0.8s ease;
        }

        .story-content.animate-in {
            opacity: 1;
            transform: translateY(0);
        }

        .story-text h2 {
            font-size: 3rem;
            margin-bottom: 2rem;
            color: var(--text-dark);
        }

        .story-text p {
            font-size: 1.1rem;
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
            line-height: 1.8;
        }

        .story-image {
            position: relative;
            height: 500px;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .story-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s ease;
        }

        .story-image:hover img {
            transform: scale(1.05);
        }

        /* ===== FLOATING ELEMENTS ===== */
        .floating-element {
            position: absolute;
            opacity: 0.1;
            pointer-events: none;
            will-change: transform;
        }

        .floating-element.shape-1 {
            top: 20%;
            left: 10%;
            width: 100px;
            height: 100px;
            background: var(--accent-color);
            border-radius: 50%;
        }

        .floating-element.shape-2 {
            top: 60%;
            right: 15%;
            width: 80px;
            height: 80px;
            background: var(--accent-secondary);
            transform: rotate(45deg);
        }

        .floating-element.shape-3 {
            bottom: 30%;
            left: 20%;
            width: 60px;
            height: 60px;
            background: var(--gradient-primary);
            border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        }

        /* ===== CTA SECTION ===== */
        .cta-section {
            background: var(--gradient-primary);
            color: var(--text-light);
            text-align: center;
            padding: 6rem 2rem;
        }

        .cta-content h2 {
            font-size: 3rem;
            margin-bottom: 2rem;
        }

        .cta-content p {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .btn {
            display: inline-block;
            padding: 1rem 2rem;
            background: rgba(255, 255, 255, 0.2);
            color: var(--text-light);
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
        }

        /* ===== RESPONSIVE DESIGN ===== */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero h1 {
                font-size: 2.5rem;
            }

            .hero .tagline {
                font-size: 1.2rem;
            }

            .story-content {
                grid-template-columns: 1fr;
                gap: 2rem;
            }

            .story-text h2 {
                font-size: 2rem;
            }

            .story-image {
                height: 300px;
            }

            .parallax-bg {
                background-attachment: scroll;
            }
        }
    </style>
</head>
<body>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress"></div>

    <!-- Sticky Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">TRIO</div>
            <ul class="nav-links">
                <li><a href="#hero" class="nav-link active">Home</a></li>
                <li><a href="#story1" class="nav-link">Origin</a></li>
                <li><a href="#story2" class="nav-link">Vision</a></li>
                <li><a href="#story3" class="nav-link">Future</a></li>
                <li><a href="#cta" class="nav-link">Join Us</a></li>
            </ul>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="hero" class="parallax-section hero">
        <div class="parallax-bg" data-speed="0.5"></div>
        <div class="parallax-content">
            <h1>TRIO</h1>
            <div class="tagline">SYRIAN MADE</div>
            <p>Where creativity meets craftsmanship. Discover the story behind modern streetwear that speaks to your soul.</p>
        </div>

        <!-- Floating Elements -->
        <div class="floating-element shape-1" data-speed="0.3"></div>
        <div class="floating-element shape-2" data-speed="0.7"></div>
        <div class="floating-element shape-3" data-speed="0.4"></div>
    </section>

    <!-- Story Section 1: Origin -->
    <section id="story1" class="story-section">
        <div class="story-content">
            <div class="story-text">
                <h2>Our Origin</h2>
                <p>Born from the vibrant streets of Syria, Trio represents more than just clothing – it's a testament to resilience, creativity, and the power of dreams.</p>
                <p>Three friends, united by their passion for design and their vision of bringing authentic Middle Eastern streetwear to the world, started this journey with nothing but determination and a sewing machine.</p>
                <p>Every stitch tells a story. Every design carries the essence of our heritage, reimagined for the modern world.</p>
            </div>
            <div class="story-image">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'><rect width='400' height='300' fill='%23a855f7'/><text x='200' y='150' text-anchor='middle' fill='white' font-size='20' font-family='Arial'>Origin Story Image</text></svg>" alt="Origin Story">
            </div>
        </div>
        <div class="floating-element shape-1" data-speed="0.2"></div>
    </section>

    <!-- Parallax Section 2 -->
    <section class="parallax-section">
        <div class="parallax-bg" data-speed="0.6" style="background: linear-gradient(45deg, #c084fc, #e879f9); opacity: 0.9;"></div>
        <div class="parallax-content">
            <h2 style="font-size: 2.5rem; margin-bottom: 1rem;">Crafted with Purpose</h2>
            <p style="font-size: 1.2rem;">Every piece in our collection is designed with intention, created with passion, and delivered with pride.</p>
        </div>
        <div class="floating-element shape-2" data-speed="0.8"></div>
        <div class="floating-element shape-3" data-speed="0.4"></div>
    </section>

    <!-- Story Section 2: Vision -->
    <section id="story2" class="story-section">
        <div class="story-content">
            <div class="story-image">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'><rect width='400' height='300' fill='%23c084fc'/><text x='200' y='150' text-anchor='middle' fill='white' font-size='20' font-family='Arial'>Vision Image</text></svg>" alt="Our Vision">
            </div>
            <div class="story-text">
                <h2>Our Vision</h2>
                <p>We envision a world where fashion transcends borders, where cultural heritage meets contemporary style, and where every individual can express their unique identity through what they wear.</p>
                <p>Trio is not just about creating clothes – we're building a community, fostering creativity, and proving that great design knows no boundaries.</p>
                <p>From the bustling markets of Damascus to the fashion capitals of the world, our vision is to make Syrian craftsmanship a symbol of excellence and innovation.</p>
            </div>
        </div>
        <div class="floating-element shape-3" data-speed="0.3"></div>
    </section>

    <!-- Parallax Section 3 -->
    <section class="parallax-section">
        <div class="parallax-bg" data-speed="0.4" style="background: linear-gradient(135deg, #2d1b3d, #4c1d95); opacity: 0.95;"></div>
        <div class="parallax-content" style="background: rgba(45, 27, 61, 0.9); color: white;">
            <h2 style="font-size: 2.5rem; margin-bottom: 1rem; color: #c084fc;">Innovation Meets Tradition</h2>
            <p style="font-size: 1.2rem; opacity: 0.9;">Blending time-honored techniques with cutting-edge design, we create pieces that honor the past while embracing the future.</p>
        </div>
        <div class="floating-element shape-1" data-speed="0.6"></div>
    </section>

    <!-- Story Section 3: Future -->
    <section id="story3" class="story-section">
        <div class="story-content">
            <div class="story-text">
                <h2>The Future</h2>
                <p>As we look ahead, Trio is committed to sustainable fashion, ethical production, and empowering the next generation of Middle Eastern designers.</p>
                <p>We're expanding our reach, building partnerships with local artisans, and creating opportunities for young creatives to showcase their talents on a global stage.</p>
                <p>The future of fashion is inclusive, diverse, and authentic – and Trio is proud to be leading that charge.</p>
            </div>
            <div class="story-image">
                <img src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 400 300'><rect width='400' height='300' fill='%23e879f9'/><text x='200' y='150' text-anchor='middle' fill='white' font-size='20' font-family='Arial'>Future Vision</text></svg>" alt="The Future">
            </div>
        </div>
        <div class="floating-element shape-2" data-speed="0.5"></div>
    </section>

    <!-- CTA Section -->
    <section id="cta" class="cta-section">
        <div class="cta-content">
            <h2>Join Our Story</h2>
            <p>Be part of the Trio journey. Experience fashion that tells a story, celebrates heritage, and embraces the future.</p>
            <a href="#" class="btn">Explore Collection</a>
        </div>
    </section>

    <script>
        // ===== PARALLAX SCROLLING EFFECT =====
        let ticking = false;

        function updateParallax() {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('[data-speed]');

            parallaxElements.forEach(element => {
                const speed = element.dataset.speed;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });

            ticking = false;
        }

        function requestTick() {
            if (!ticking) {
                requestAnimationFrame(updateParallax);
                ticking = true;
            }
        }

        // ===== SCROLL PROGRESS INDICATOR =====
        function updateScrollProgress() {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            document.querySelector('.scroll-progress').style.width = scrollPercent + '%';
        }

        // ===== STICKY NAVIGATION =====
        function updateNavigation() {
            const navbar = document.querySelector('.navbar');
            const scrolled = window.pageYOffset;

            if (scrolled > 100) {
                navbar.classList.add('visible');
            } else {
                navbar.classList.remove('visible');
            }

            // Update active nav link
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop - 200;
                if (scrolled >= sectionTop) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        }

        // ===== INTERSECTION OBSERVER FOR ANIMATIONS =====
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // ===== SMOOTH SCROLLING FOR NAVIGATION =====
        function initSmoothScrolling() {
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const targetId = link.getAttribute('href');
                    const targetSection = document.querySelector(targetId);
                    if (targetSection) {
                        targetSection.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // ===== INITIALIZE EVERYTHING =====
        document.addEventListener('DOMContentLoaded', () => {
            // Observe elements for animation
            const animateElements = document.querySelectorAll('.parallax-content, .story-content');
            animateElements.forEach(el => observer.observe(el));

            // Initialize smooth scrolling
            initSmoothScrolling();

            // Initial calls
            updateParallax();
            updateScrollProgress();
            updateNavigation();
        });

        // ===== SCROLL EVENT LISTENERS =====
        window.addEventListener('scroll', () => {
            requestTick();
            updateScrollProgress();
            updateNavigation();
        });

        // ===== RESIZE HANDLER =====
        window.addEventListener('resize', () => {
            requestTick();
        });
    </script>
</body>
</html>
