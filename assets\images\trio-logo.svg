<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 120" width="300" height="120">
  <defs>
    <linearGradient id="purpleGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#a855f7;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#c084fc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e879f9;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Hanger curve -->
  <path d="M 40 25 Q 150 15 260 25" stroke="url(#purpleGradient)" stroke-width="4" fill="none" stroke-linecap="round"/>

  <!-- T-shirt hanging -->
  <g transform="translate(35, 30)">
    <path d="M 8 8 L 8 25 L 22 25 L 22 8 L 20 8 L 20 3 L 18 3 L 18 0 L 12 0 L 12 3 L 10 3 L 10 8 Z" fill="url(#purpleGradient)"/>
  </g>

  <!-- "Trio" text in script style -->
  <text x="80" y="65" font-family="cursive" font-size="36" font-weight="bold" fill="url(#purpleGradient)" font-style="italic">Trio</text>

  <!-- Decorative swirl -->
  <path d="M 180 45 Q 190 40 200 50 Q 205 60 200 70" stroke="url(#purpleGradient)" stroke-width="2" fill="none" stroke-linecap="round"/>

  <!-- "SYRIAN MADE" text -->
  <text x="50" y="95" font-family="Arial, sans-serif" font-size="10" font-weight="bold" letter-spacing="4" fill="url(#purpleGradient)">SYRIAN MADE</text>

  <!-- Underline -->
  <line x1="50" y1="85" x2="250" y2="85" stroke="url(#purpleGradient)" stroke-width="2"/>
</svg>
